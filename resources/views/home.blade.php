@extends('layouts.app')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Hero Section -->
    <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-gray-900 mb-4">Design Portfolio</h1>
        <p class="text-xl text-gray-600 max-w-2xl mx-auto">
            Explore my collection of unique liveries and designs. Each piece is crafted with attention to detail and available for purchase.
        </p>
    </div>

    <!-- Search and Filter Section -->
    <div class="mb-8">
        <form method="GET" action="{{ route('home') }}" class="flex flex-col sm:flex-row gap-4 max-w-2xl mx-auto">
            <div class="flex-1">
                <input 
                    type="text" 
                    name="search" 
                    value="{{ request('search') }}"
                    placeholder="Search liveries..." 
                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                >
            </div>
            <div class="sm:w-48">
                <select 
                    name="category" 
                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                >
                    <option value="">All Categories</option>
                    @foreach($categories as $category)
                        <option value="{{ $category }}" {{ request('category') == $category ? 'selected' : '' }}>
                            {{ ucfirst($category) }}
                        </option>
                    @endforeach
                </select>
            </div>
            <button 
                type="submit" 
                class="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
            >
                Search
            </button>
        </form>
    </div>

    <!-- Results Count -->
    @if(request('search') || request('category'))
        <div class="mb-6 text-center">
            <p class="text-gray-600">
                Found {{ $liveries->count() }} result{{ $liveries->count() !== 1 ? 's' : '' }}
                @if(request('search'))
                    for "{{ request('search') }}"
                @endif
                @if(request('category'))
                    in {{ ucfirst(request('category')) }}
                @endif
            </p>
            <a href="{{ route('home') }}" class="text-indigo-600 hover:text-indigo-800 underline">Clear filters</a>
        </div>
    @endif

    <!-- Liveries Grid -->
    <div class="bg-white">
        <div class="mx-auto max-w-2xl px-4 py-16 sm:px-6 sm:py-24 lg:max-w-7xl lg:px-8">
            <div class="flex items-center justify-between space-x-4 mb-6">
                <h2 class="text-lg font-medium text-gray-900">Featured Liveries</h2>
                @if(request('search') || request('category'))
                    <a href="{{ route('home') }}" class="whitespace-nowrap text-sm font-medium text-indigo-600 hover:text-indigo-500">
                        View all
                        <span aria-hidden="true"> &rarr;</span>
                    </a>
                @endif
            </div>

            <div id="portfolio" class="grid grid-cols-1 gap-x-8 gap-y-8 sm:grid-cols-2 sm:gap-y-10 lg:grid-cols-4">
                @forelse($liveries as $livery)
                    <div class="group relative">
                        <div class="relative">
                            @if($livery->image_path)
                                <img
                                    src="{{ asset('storage/' . $livery->image_path) }}"
                                    alt="{{ $livery->name }}"
                                    class="aspect-[4/3] w-full rounded-lg bg-gray-100 object-cover"
                                >
                            @else
                                <div class="aspect-[4/3] w-full rounded-lg bg-gradient-to-br from-indigo-400 to-purple-500 flex items-center justify-center">
                                    <span class="text-white text-lg font-semibold">{{ substr($livery->name, 0, 2) }}</span>
                                </div>
                            @endif

                            @if($livery->featured)
                                <div class="absolute top-2 left-2">
                                    <span class="bg-yellow-400 text-yellow-900 px-2 py-1 rounded-full text-xs font-semibold">
                                        Featured
                                    </span>
                                </div>
                            @endif

                            <!-- Hover overlay with View Product button -->
                            <div class="absolute inset-0 flex items-end p-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300" aria-hidden="true">
                                <div class="w-full rounded-md bg-white/75 px-4 py-2 text-center text-sm font-medium text-gray-900 backdrop-blur backdrop-filter">
                                    View Product
                                </div>
                            </div>
                        </div>

                        <div class="mt-4 flex items-center justify-between space-x-8 text-base font-medium text-gray-900">
                            <h3>
                                <a href="#">
                                    <span aria-hidden="true" class="absolute inset-0"></span>
                                    {{ $livery->name }}
                                </a>
                            </h3>
                            @if($livery->for_sale && $livery->price)
                                <p>${{ number_format($livery->price, 0) }}</p>
                            @elseif($livery->for_sale)
                                <p class="text-sm">Contact</p>
                            @else
                                <p class="text-sm text-gray-500">N/A</p>
                            @endif
                        </div>

                        <p class="mt-1 text-sm text-gray-500">
                            @if($livery->category)
                                {{ ucfirst($livery->category) }}
                            @else
                                Livery Design
                            @endif
                        </p>
                    </div>
                @empty
                    <div class="col-span-full text-center py-12">
                        <div class="text-gray-400 mb-4">
                            <svg class="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.47-.881-6.08-2.33" />
                            </svg>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No liveries found</h3>
                        <p class="text-gray-500">
                            @if(request('search') || request('category'))
                                Try adjusting your search criteria or <a href="{{ route('home') }}" class="text-indigo-600 hover:text-indigo-800 underline">browse all liveries</a>.
                            @else
                                Check back soon for new designs!
                            @endif
                        </p>
                    </div>
                @endforelse
            </div>
        </div>
    </div>
    </div>
</div>

<script>
// Real-time search functionality
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.querySelector('input[name="search"]');
    const categorySelect = document.querySelector('select[name="category"]');
    
    // Auto-submit form on category change
    categorySelect.addEventListener('change', function() {
        this.form.submit();
    });
    
    // Optional: Add debounced search
    let searchTimeout;
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            // You can implement AJAX search here if needed
        }, 500);
    });
});
</script>
@endsection
