@extends('layouts.app')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Hero Section -->
    <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-gray-900 mb-4">Design Portfolio</h1>
        <p class="text-xl text-gray-600 max-w-2xl mx-auto">
            Explore my collection of unique liveries and designs. Each piece is crafted with attention to detail and available for purchase.
        </p>
    </div>

    <!-- Search and Filter Section -->
    <div class="mb-8">
        <form method="GET" action="{{ route('home') }}" class="flex flex-col sm:flex-row gap-4 max-w-2xl mx-auto">
            <div class="flex-1">
                <input 
                    type="text" 
                    name="search" 
                    value="{{ request('search') }}"
                    placeholder="Search liveries..." 
                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                >
            </div>
            <div class="sm:w-48">
                <select 
                    name="category" 
                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                >
                    <option value="">All Categories</option>
                    @foreach($categories as $category)
                        <option value="{{ $category }}" {{ request('category') == $category ? 'selected' : '' }}>
                            {{ ucfirst($category) }}
                        </option>
                    @endforeach
                </select>
            </div>
            <button 
                type="submit" 
                class="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
            >
                Search
            </button>
        </form>
    </div>

    <!-- Results Count -->
    @if(request('search') || request('category'))
        <div class="mb-6 text-center">
            <p class="text-gray-600">
                Found {{ $liveries->count() }} result{{ $liveries->count() !== 1 ? 's' : '' }}
                @if(request('search'))
                    for "{{ request('search') }}"
                @endif
                @if(request('category'))
                    in {{ ucfirst(request('category')) }}
                @endif
            </p>
            <a href="{{ route('home') }}" class="text-indigo-600 hover:text-indigo-800 underline">Clear filters</a>
        </div>
    @endif

    <!-- Liveries Grid -->
    <div id="portfolio" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        @forelse($liveries as $livery)
            <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
                <div class="aspect-w-16 bg-gray-200">
                    @if($livery->image_path)
                        <img 
                            src="{{ asset('storage/' . $livery->image_path) }}" 
                            alt="{{ $livery->name }}"
                            class="w-full h-48 object-cover"
                        >
                    @else
                        <div class="w-full h-48 bg-gradient-to-br from-indigo-400 to-purple-500 flex items-center justify-center">
                            <span class="text-white text-lg font-semibold">{{ substr($livery->name, 0, 2) }}</span>
                        </div>
                    @endif
                    @if($livery->featured)
                        <div class="absolute top-2 left-2">
                            <span class="bg-yellow-400 text-yellow-900 px-2 py-1 rounded-full text-xs font-semibold">
                                Featured
                            </span>
                        </div>
                    @endif
                </div>
                <div class="p-4">
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ $livery->name }}</h3>
                    <p class="text-gray-600 text-sm mb-3 line-clamp-2">{{ $livery->description }}</p>
                    
                    <div class="flex items-center justify-between">
                        <div>
                            @if($livery->category)
                                <span class="inline-block bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-xs">
                                    {{ ucfirst($livery->category) }}
                                </span>
                            @endif
                        </div>
                        <div class="text-right">
                            @if($livery->for_sale && $livery->price)
                                <p class="text-lg font-bold text-indigo-600">${{ number_format($livery->price, 2) }}</p>
                                <button class="mt-1 bg-indigo-600 text-white px-3 py-1 rounded text-sm hover:bg-indigo-700">
                                    Buy Now
                                </button>
                            @elseif($livery->for_sale)
                                <button class="bg-indigo-600 text-white px-3 py-1 rounded text-sm hover:bg-indigo-700">
                                    Contact for Price
                                </button>
                            @else
                                <span class="text-gray-500 text-sm">Not for Sale</span>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        @empty
            <div class="col-span-full text-center py-12">
                <div class="text-gray-400 mb-4">
                    <svg class="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.47-.881-6.08-2.33" />
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No liveries found</h3>
                <p class="text-gray-500">
                    @if(request('search') || request('category'))
                        Try adjusting your search criteria or <a href="{{ route('home') }}" class="text-indigo-600 hover:text-indigo-800 underline">browse all liveries</a>.
                    @else
                        Check back soon for new designs!
                    @endif
                </p>
            </div>
        @endforelse
    </div>
</div>

<script>
// Real-time search functionality
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.querySelector('input[name="search"]');
    const categorySelect = document.querySelector('select[name="category"]');
    
    // Auto-submit form on category change
    categorySelect.addEventListener('change', function() {
        this.form.submit();
    });
    
    // Optional: Add debounced search
    let searchTimeout;
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            // You can implement AJAX search here if needed
        }, 500);
    });
});
</script>
@endsection
